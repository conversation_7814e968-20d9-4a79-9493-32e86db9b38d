{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import numpy as np\n", "import torch\n", "from os import path\n", "import sys\n", "import tqdm\n", "import imageio.v3 as iio\n", "import cv2 as cv\n", "\n", "from torchmetrics.functional.image import peak_signal_noise_ratio, structural_similarity_index_measure\n", "from torchmetrics.image import LearnedPerceptualImagePatchSimilarity, StructuralSimilarityIndexMeasure, PeakSignalNoiseRatio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ssim_model = StructuralSimilarityIndexMeasure(data_range=1.0).cuda()\n", "psnr_model = PeakSignalNoiseRatio(data_range=1.0).cuda()\n", "lpips_model = LearnedPerceptualImagePatchSimilarity(net_type=\"vgg\", normalize=True).cuda()\n", "\n", "# code from AnimatableGaussians\n", "def crop_image(gt_mask, patch_size, *args):\n", "    \"\"\"\n", "    :param gt_mask: (H, W)\n", "    :param patch_size: resize the cropped patch to the given patch_size\n", "    :param args: some images with shape of (H, W, C)\n", "    \"\"\"\n", "    mask_uv = np.argwhere(gt_mask > 0.)\n", "    min_v, min_u = mask_uv.min(0)\n", "    max_v, max_u = mask_uv.max(0)\n", "    pad_size = 0\n", "    min_v = (min_v - pad_size).clip(0, gt_mask.shape[0])\n", "    min_u = (min_u - pad_size).clip(0, gt_mask.shape[1])\n", "    max_v = (max_v + pad_size).clip(0, gt_mask.shape[0])\n", "    max_u = (max_u + pad_size).clip(0, gt_mask.shape[1])\n", "    len_v = max_v - min_v\n", "    len_u = max_u - min_u\n", "    max_size = max(len_v, len_u)\n", "\n", "    cropped_images = []\n", "    for image in args:\n", "        if image is None:\n", "            cropped_images.append(None)\n", "        else:\n", "            cropped_image = np.ones((max_size, max_size, 3), dtype = image.dtype)\n", "            if len_v > len_u:\n", "                start_u = (max_size - len_u) // 2\n", "                cropped_image[:, start_u: start_u + len_u] = image[min_v: max_v, min_u: max_u]\n", "            else:\n", "                start_v = (max_size - len_v) // 2\n", "                cropped_image[start_v: start_v + len_v, :] = image[min_v: max_v, min_u: max_u]\n", "\n", "            cropped_image = cv.resize(cropped_image, (patch_size, patch_size), interpolation = cv.INTER_LINEAR)\n", "            cropped_images.append(cropped_image)\n", "\n", "    if len(cropped_images) > 1:\n", "        return cropped_images\n", "    else:\n", "        return cropped_images[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_dir = ''\n", "\n", "import skimage.morphology as sm\n", "from torch.utils.data import DataLoader\n", "\n", "class MyDataset:\n", "    def __init__(self, gt_paths, render_paths, mask_paths):\n", "        self.gt_paths = gt_paths\n", "        self.render_paths = render_paths\n", "        self.mask_paths = mask_paths\n", "    def __len__(self):\n", "        return len(self.gt_paths)\n", "    def __getitem__(self, idx):\n", "        im_gt = iio.imread(self.gt_paths[idx])\n", "        im_gsbody = iio.imread(self.render_paths[idx])\n", "        mask = iio.imread(self.mask_paths[idx])\n", "        return im_gt, im_gsbody, mask\n", "\n", "filenames = os.listdir(path.join(data_dir, 'gt'))\n", "filenames.sort()\n", "gt_paths = [path.join(data_dir, f'gt/{filename}') for filename in filenames]\n", "mask_paths = [path.join(data_dir, f'mask/{filename}') for filename in filenames]\n", "render_names = os.listdir(path.join(data_dir, 'render'))\n", "render_names.sort()\n", "render_paths = [path.join(data_dir, f'render/{filename}') for filename in render_names]\n", "dataset = MyDataset(gt_paths, render_paths, mask_paths)\n", "dataloader = DataLoader(dataset=dataset, batch_size=None, num_workers=8, collate_fn=lambda x: x)\n", "\n", "lpips_list = []\n", "ssim_list = []\n", "psnr_list = []\n", "\n", "for data in tqdm.tqdm(dataloader):\n", "    im_gt, im_gsbody, mask = data\n", "    assert len(mask.shape) == 2\n", "    mask = mask > 128\n", "\n", "    im_gt_crop, im_gsbody_crop = crop_image(mask, 512, im_gt, im_gsbody)\n", "\n", "    im_gt = torch.tensor(im_gt / 255).permute(2,0,1).float().cuda()[None]\n", "    im_gsbody = torch.tensor(im_gsbody / 255).permute(2,0,1).float().cuda()[None]\n", "\n", "    im_gt_crop = torch.tensor(im_gt_crop / 255).permute(2,0,1).float().cuda()[None]\n", "    im_gsbody_crop = torch.tensor(im_gsbody_crop / 255).permute(2,0,1).float().cuda()[None]\n", "\n", "    psnr_value = psnr_model(im_gsbody, im_gt).item()\n", "    ssim_value = ssim_model(im_gsbody, im_gt).item()\n", "    lpips_value = lpips_model(im_gsbody_crop, im_gt_crop).item()\n", "\n", "    psnr_list.append(psnr_value)\n", "    ssim_list.append(ssim_value)\n", "    lpips_list.append(lpips_value)\n", "    \n", "psnr_list = np.array(psnr_list)\n", "ssim_list = np.array(ssim_list)\n", "lpips_list = np.array(lpips_list)\n", "\n", "print(psnr_list.mean())\n", "print(ssim_list.mean())\n", "print(lpips_list.mean())\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}